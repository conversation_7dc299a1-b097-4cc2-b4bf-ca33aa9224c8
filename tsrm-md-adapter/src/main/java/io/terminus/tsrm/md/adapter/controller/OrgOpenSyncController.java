package io.terminus.tsrm.md.adapter.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.terminus.trantor2.common.dto.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @time 2025/8/26 11:02
 */
@Api(tags = "供应商同步晶澳数据")
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/tsrm/md/org/open/sync")
public class OrgOpenSyncController {

    @ApiOperation("VEND-同步新增组织")
    @PostMapping(value = "addOrg")
    public void addOrg(@RequestBody VendPortalEntryDTO vendPortalEntryDTO, HttpServletResponse response){
    }
}
